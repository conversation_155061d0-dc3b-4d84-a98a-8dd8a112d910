import type { IpcRendererEvent } from 'electron';

declare global {
    interface Window {
        electronAPI: {
            completeWelcome: () => void;
            onMessage: (callback: (event: IpcRendererEvent, ...args: any[]) => void) => void;
            getSystemInfo: () => {
                platform: string;
                version: string;
                isDev: boolean;
                isMacOS: boolean;
            };
            setTheme: (theme: string) => void;
            importConfig: (source: string) => Promise<void>;
            installCLI: () => Promise<void>;
            login: () => Promise<void>;
            checkThirypartyIDEInstalled: (source: string) => boolean;
        };
        func: {
            completeWelcome: () => void;
            skipWelcome: () => void;
            selectTheme: (theme: string) => void;
            importFrom: (source: string) => void;
            installCLI: () => Promise<void>;
            login: () => Promise<void>;
            nextSlide: () => void;
            prevSlide: () => void;
            goToSlide: (index: number) => void;
        };
    }
}
